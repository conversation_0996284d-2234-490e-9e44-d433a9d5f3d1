"use client";

import { useState } from "react";

import { ArrowRightIcon, ChevronRightIcon } from "lucide-react";
import { motion } from "motion/react";

import { Badge } from "@/components/ui/badge";

export default function Waitlist() {
  const [email, setEmail] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) return;

    setIsLoading(true);

    // Simulate API call - replace with your actual waitlist API
    await new Promise((resolve) => setTimeout(resolve, 1000));

    setIsSubmitted(true);
    setIsLoading(false);
  };

  return (
    <section className="bg-background relative px-4 py-32 sm:px-6 lg:px-8">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, amount: 0.1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="mx-auto max-w-4xl text-center"
      >
        {/* Category Label with Green Dot - Linear Style */}
        <Badge className="mx-auto mb-8 flex items-center justify-center rounded-full px-4 py-2">
          Early access
          <ChevronRightIcon className="h-4 w-4" />
        </Badge>

        {/* Main Heading - Linear Style */}
        <h2 className="from-primary via-primary/80 to-primary/70 mb-8 bg-gradient-to-r bg-clip-text text-5xl font-medium tracking-tight text-transparent sm:text-6xl">
          Join the waitlist
        </h2>

        {/* Enhanced Description - Linear Style */}
        <div className="mx-auto mb-12 max-w-2xl">
          <p className="text-muted-foreground text-lg leading-relaxed">
            <span className="text-foreground font-medium">
              Be among the first to experience the future of product
              development.
            </span>{" "}
            Get exclusive early access and help shape the product.
          </p>
        </div>

        {/* Waitlist Form */}
        {!isSubmitted ? (
          <div className="mx-auto max-w-md">
            <form
              onSubmit={handleSubmit}
              className="flex flex-col gap-3 sm:flex-row"
            >
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email address"
                required
                className="bg-background hover:bg-muted/50 dark:ring-input border-input/50 dark:border-input relative h-12 w-full rounded-lg border-b-2 px-4 py-2 shadow-sm ring-1 shadow-zinc-950/15 ring-zinc-300"
              />
              <button
                type="submit"
                disabled={isLoading || !email}
                className="from-primary to-primary/85 text-primary-foreground flex h-12 shrink-0 items-center justify-center gap-2 rounded-lg border border-b-2 border-zinc-950/40 bg-linear-to-t px-4 shadow-md ring-1 shadow-zinc-950/20 ring-white/25 transition-[filter] duration-200 ring-inset hover:brightness-110 active:brightness-90 dark:border-x-0 dark:border-t-0 dark:border-zinc-950/50 dark:inset-shadow-2xs dark:ring-white/5 dark:inset-shadow-white/10"
              >
                {isLoading ? (
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                ) : (
                  <>
                    Join waitlist
                    <ArrowRightIcon className="h-4 w-4" />
                  </>
                )}
              </button>
            </form>
          </div>
        ) : (
          <div className="mx-auto max-w-md">
            <div className="bg-muted/50 border-border rounded-lg border p-6">
              <p className="text-muted-foreground text-sm leading-relaxed">
                Thanks for joining our waitlist. We&apos;ll notify you as soon
                as we launch.
              </p>
            </div>
          </div>
        )}
      </motion.div>
    </section>
  );
}
