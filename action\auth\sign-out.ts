"use server";

import { headers } from "next/headers";

import GetAuthSession from "@/action/auth/get-auth-session";
import { auth } from "@/auth";

export default async function SignOut() {
  const session = await GetAuthSession();

  if (!session) {
    return { error: "No session found" };
  }

  await auth.api.signOut({
    headers: await headers(),
  });

  return { success: "Signed out successfully" };
}
