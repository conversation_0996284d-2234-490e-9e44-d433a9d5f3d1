// prisma.ts
import { PrismaClient } from "@prisma/client";

// Extend the globalThis interface to include the prisma property
declare global {
  var prisma: PrismaClient | undefined;
}

// Use a const for the PrismaClient instance
const prisma =
  global.prisma ||
  new PrismaClient({
    log: ["query", "info", "warn", "error"], // Adjust logging as needed
  });

// Assign to global only if not already set
if (!global.prisma) {
  global.prisma = prisma;
}

export default prisma;
