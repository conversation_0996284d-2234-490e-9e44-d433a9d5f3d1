"use server";

import nodemailer from "nodemailer";

interface SendMagicLinkEmailParams {
  email: string;
  url: string;
}

export default async function SendMagicLinkEmail({
  email,
  url,
}: SendMagicLinkEmailParams) {
  try {
    // Create transporter using Gmail SMTP
    const transporter = nodemailer.createTransport({
      service: "gmail",
      auth: {
        user: process.env.SMTP_USER as string,
        pass: process.env.SMTP_PASS as string,
      },
    });

    // Email template with Inter font and landing page typography styling (email-safe version)
    const htmlTemplate = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Sign in to your account</title>
        <!--[if !mso]><!-->
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
        <!--<![endif]-->
        <style>
          /* Email-safe CSS with fallbacks */
          @media screen and (max-width: 600px) {
            .mobile-padding {
              padding: 24px 16px !important;
            }
            .mobile-header-padding {
              padding: 32px 20px 24px !important;
            }
            .mobile-content-padding {
              padding: 0 20px 28px !important;
            }
            .mobile-title {
              font-size: 22px !important;
              line-height: 1.3 !important;
            }
            .mobile-subtitle {
              font-size: 16px !important;
            }
            .mobile-button {
              padding: 12px 24px !important;
              font-size: 16px !important;
              min-width: 140px !important;
            }
            .mobile-brand {
              font-size: 26px !important;
            }
          }
        </style>
      </head>
      <body style="font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif; line-height: 1.6; color: #24292f; background-color: #ffffff; margin: 0; padding: 0; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;">
        <div class="mobile-padding" style="background-color: #ffffff; padding: 32px 20px;">
          <div style="max-width: 580px; margin: 0 auto; background: #ffffff; border-radius: 10px; border: 1px solid #e1e4e8; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);">
            <div class="mobile-header-padding" style="text-align: center; padding: 32px 24px 20px; background: #ffffff;">
              <div class="mobile-brand" style="font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; font-size: 24px; font-weight: 600; color: #24292f; margin-bottom: 16px; letter-spacing: -0.025em;">Next Core</div>
              <h1 class="mobile-title" style="font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; font-size: 26px; font-weight: 500; margin: 0 0 10px 0; color: #24292f; letter-spacing: -0.025em; line-height: 1.3;">Sign in to your account</h1>
              <p class="mobile-subtitle" style="font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; color: #656d76; margin: 0; font-size: 16px; font-weight: 400; line-height: 1.5;">Click the button below to securely sign in</p>
            </div>

            <div class="mobile-content-padding" style="padding: 0 32px 32px;">
              <div style="text-align: center; margin: 24px 0;">
                <a href="${url}" class="mobile-button" style="font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; display: inline-block; background: #24292f; color: #ffffff !important; padding: 14px 28px; text-decoration: none; border-radius: 8px; font-weight: 600; font-size: 16px; border: none; box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05); min-width: 160px;">Sign In</a>
              </div>

              <div style="font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f6f8fa; border: 1px solid #e1e4e8; border-radius: 8px; padding: 18px; margin: 20px 0; font-size: 14px; color: #656d76; line-height: 1.6;">
                <strong style="color: #24292f; font-weight: 600;">Security Notice:</strong> This link will expire in 15 minutes and can only be used once.
                If you didn't request this email, you can safely ignore it.
              </div>

              <div style="font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; margin-top: 24px; padding-top: 20px; border-top: 1px solid #e1e4e8; font-size: 14px; color: #656d76; text-align: center; line-height: 1.6;">
                <p style="margin: 0 0 12px 0;">If the button doesn't work, copy and paste this link into your browser:</p>
                <div style="word-break: break-all; color: #24292f !important; background: #f6f8fa; padding: 12px 16px; border-radius: 6px; border: 1px solid #e1e4e8; font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace; font-size: 13px; margin: 12px 0; display: block;">${url}</div>
                <p style="margin: 0;">This email was sent to ${email}. If you have any questions, please contact support.</p>
              </div>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;

    const textTemplate = `
Next Core - Sign in to your account

Click the link below to securely sign in to your account:
${url}

SECURITY NOTICE:
This link will expire in 15 minutes and can only be used once.
If you didn't request this email, you can safely ignore it.

If the link doesn't work, copy and paste it into your browser.

This email was sent to ${email}. If you have any questions, please contact support.

---
Next Core Team
    `;

    // Send email
    const info = await transporter.sendMail({
      from: `"Next Core" <${process.env.SMTP_USER}>`,
      to: email,
      subject: "Sign in to your account",
      text: textTemplate,
      html: htmlTemplate,
    });

    console.log("Magic link email sent:", info.messageId);
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error("Failed to send magic link email:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to send email",
    };
  }
}
