# Authentication Preferences

- User prefers simplified authentication forms that only support sign-in functionality without mode switching between sign-in and sign-up.
- User prefers using authClient.signIn.magicLink({ email, callbackURL }) method from better-auth client for magic link authentication instead of server actions.

# Design Preferences

- User prefers landing page designs that follow Linear.app's design principles and aesthetic approach.
- User prefers more sophisticated and detailed designs over basic layouts for UI components.
- User prefers minimalistic and stable visual designs over card-based layouts for UI components.
- User prefers minimalistic visual designs using simple and profound divs, similar to the structure of feature 1, rather than complex animated elements.
- User prefers features section visuals to be generic, simple, static, minimalistic, and Linear-styled like feature 1, rather than complex animated designs.
- User prefers gamified design elements (progress bars, achievement badges, level indicators, point counters) integrated into features sections while maintaining minimalistic Linear-styled aesthetics and professional appearance.
- User likes the top-nav's design and wants the theme-switcher updated to follow the same design approach.
- User prefers interactive diagrams for feature 2 and floating elements design for feature 3 instead of traditional layouts.
- User prefers center nav links to have fixed positioning that doesn't adjust based on side space, and dropdown menus should follow Linear.app design principles.
- User prefers email components to utilize Linear.app's design principles and styling with Plus Jakarta Sans font.
- User prefers reduced padding in email templates, finding the current padding too excessive.
- User prefers Inter font for email templates and wants email typography to match the landing page styling patterns.
- User prefers to prioritize improved micro-interactions and premium visual effects (like better shadows and glass morphism) for landing page enhancements.
- User prefers premium visual elements with realistic shadows and depth, better glass morphism effects, and more sophisticated border treatments for enhanced UI design.
- User prefers professional animations over playful or dynamic animation styles.
- User prefers scrolling to be contained within child elements rather than the body element, with inset containers managing their own scrolling behavior.

# Font Preferences

- User prefers to keep Plus Jakarta Sans font instead of switching to Inter for the pricing section design.
- User prefers to keep Inter as the default font, except for the pricing section where Plus Jakarta Sans should be used.

# Implementation Preferences

- User prioritizes security reviews, code optimization, cleanliness, and thorough validation of functionality before proceeding with implementation tasks.
- User prefers lowercase file names for components and other files.
- User prefers section headings to maintain consistent styling across all sections and wants pricing comparison tables to follow a specific layout format.

# Project Description

- User wants to build a SAAS for AI-driven call center solution with features like call flow builder, AI-powered IVR, automated agents, speech recognition, and real-time analytics to replace traditional human-operated call centers.
