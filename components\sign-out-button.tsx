"use client";

import { useTransition } from "react";

import { useRouter } from "@bprogress/next";

import SignOut from "@/action/auth/sign-out";
import ConfirmationDialog from "@/components/confirmation-dialog";
import { Button } from "@/components/ui/button";
import useDialog from "@/hooks/use-dialog";

export default function SignOutButton() {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const { open, onOpenChange, openDialog, closeDialog } = useDialog();

  function handleSignOut() {
    startTransition(async () => {
      try {
        const { success } = await SignOut();

        if (success) {
          router.refresh();
        }
      } catch (error) {
        console.error(error);
      }
    });
  }

  return (
    <>
      <Button
        variant="destructive"
        onClick={openDialog}
        disabled={isPending}
      >
        {isPending ? "Signing out..." : "Sign out"}
      </Button>

      <ConfirmationDialog
        open={open}
        onOpenChange={onOpenChange}
        closeDialog={closeDialog}
        onClick={handleSignOut}
        isPending={isPending}
        title="Are you sure you want to sign out?"
        description="This will log you out of your account"
        confirmText="Confirm"
        confirmVariant="destructive"
      />
    </>
  );
}
