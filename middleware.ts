import { betterFetch } from "@better-fetch/fetch";
import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

import type { Session } from "@/auth";
import {
  DEFAULT_LOGIN_REDIRECT,
  SIGN_IN_REDIRECT,
  adminRoute,
  apiAuthPrefix,
  authRoutes,
  isPublicRoute,
} from "@/routes";

export default async function authMiddleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Early return for API auth routes - no session needed
  if (pathname.startsWith(apiAuthPrefix)) {
    return NextResponse.next();
  }

  // Early return for public routes - no session needed
  if (isPublicRoute(pathname)) {
    return NextResponse.next();
  }

  // Check route types that require session validation
  const isAuthRoute = authRoutes.includes(pathname);
  const isAdminRoute = pathname.startsWith(adminRoute);

  try {
    const { data: session } = await betterFetch<Session>(
      "/api/auth/get-session",
      {
        baseURL: process.env.BETTER_AUTH_URL,
        headers: {
          cookie: request.headers.get("cookie") || "",
        },
        query: {
          disableCookieCache: true,
        },
      },
    );

    if (!session) {
      if (isAuthRoute) return NextResponse.next();
      return NextResponse.redirect(new URL(SIGN_IN_REDIRECT, request.url));
    }

    if (
      isAdminRoute &&
      session.user.role !== "admin" &&
      session.user.role !== "superadmin"
    ) {
      return NextResponse.redirect(
        new URL(DEFAULT_LOGIN_REDIRECT, request.url),
      );
    }

    if (isAuthRoute) {
      return NextResponse.redirect(
        new URL(DEFAULT_LOGIN_REDIRECT, request.url),
      );
    }

    return NextResponse.next();
  } catch (error) {
    console.error("Error in authMiddleware:", error);

    return NextResponse.redirect(new URL(SIGN_IN_REDIRECT, request.url));
  }
}

export const config = {
  matcher: ["/((?!api|_next/static|_next/image|favicon.ico|.*\\.png$).*)"],
};
