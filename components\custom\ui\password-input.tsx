"use client";

import * as React from "react";

import { EyeIcon, EyeOffIcon } from "lucide-react";

import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";

const PasswordInput = React.forwardRef<
  HTMLInputElement,
  React.ComponentProps<"input">
>(({ className, ...props }, ref) => {
  const disabled = props.value === "" || props.disabled;

  const [showPassword, setShowPassword] = React.useState<boolean>(false);

  const onClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault(); // Prevent any form submission or default behavior
    setShowPassword((prev) => !prev);
  };

  return (
    <div className="relative">
      <Input
        ref={ref}
        type={showPassword ? "text" : "password"}
        className={cn("hide-password-toggle pr-10", className)}
        {...props}
      />
      <button
        type="button"
        onClick={onClick}
        disabled={disabled}
        tabIndex={-1}
        className="text-muted-foreground hover:text-foreground absolute top-0 right-0 h-full items-center justify-center px-3 py-2 hover:bg-transparent"
      >
        {showPassword && !disabled ? (
          <EyeIcon
            className="!size-4"
            aria-hidden="true"
          />
        ) : (
          <EyeOffIcon
            className="!size-4"
            aria-hidden="true"
          />
        )}
        <span className="sr-only">
          {showPassword ? "Hide password" : "Show password"}
        </span>
      </button>

      <style>{`
					.hide-password-toggle::-ms-reveal,
					.hide-password-toggle::-ms-clear {
						visibility: hidden;
						pointer-events: none;
						display: none;
					}
				`}</style>
    </div>
  );
});
PasswordInput.displayName = "PasswordInput";

export { PasswordInput };
