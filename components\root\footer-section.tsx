import Link from "next/link";

import Logo from "@/components/logo";

const footerLinks = {
  Features: [
    { label: "Plan", href: "/features/plan" },
    { label: "Build", href: "/features/build" },
    { label: "Insights", href: "/features/insights" },
    { label: "Customer Requests", href: "/features/customer-requests" },
    { label: "Linear Asks", href: "/features/linear-asks" },
    { label: "Security", href: "/features/security" },
    { label: "Mobile", href: "/features/mobile" },
  ],
  Product: [
    { label: "Pricing", href: "/pricing" },
    { label: "Method", href: "/product/method" },
    { label: "Integrations", href: "/product/integrations" },
    { label: "Changelog", href: "/product/changelog" },
    { label: "Documentation", href: "/docs" },
    { label: "Download", href: "/download" },
    { label: "Switch", href: "/switch" },
  ],
  Company: [
    { label: "About", href: "/company/about" },
    { label: "Customers", href: "/company/customers" },
    { label: "Careers", href: "/company/careers" },
    { label: "Blog", href: "/blog" },
    { label: "README", href: "/readme" },
    { label: "Quality", href: "/company/quality" },
    { label: "Brand", href: "/company/brand" },
  ],
  Resources: [
    { label: "Developers", href: "/developers" },
    { label: "Status", href: "/status" },
    { label: "Startups", href: "/startups" },
    { label: "Report vulnerability", href: "/security/report" },
    { label: "DPA", href: "/legal/dpa" },
    { label: "Privacy", href: "/legal/privacy" },
    { label: "Terms", href: "/legal/terms" },
  ],
  Connect: [
    { label: "Contact us", href: "/contact" },
    { label: "Community", href: "/community" },
    { label: "X (Twitter)", href: "https://twitter.com" },
    { label: "GitHub", href: "https://github.com" },
    { label: "YouTube", href: "https://youtube.com" },
  ],
};

export default function FooterSection() {
  return (
    <footer className="bg-background border-border border-t px-4 py-16 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        <div className="grid grid-cols-2 gap-8 md:grid-cols-3 lg:grid-cols-6">
          {/* Logo */}
          <div className="flex items-start">
            <Logo />
          </div>

          {/* Footer Links */}
          {Object.entries(footerLinks).map(([category, links]) => (
            <div key={category}>
              <h3 className="text-foreground mb-4 text-sm font-medium">
                {category}
              </h3>
              <ul className="space-y-3">
                {links.map((link) => (
                  <li key={link.label}>
                    <Link
                      href={link.href}
                      className="text-muted-foreground hover:text-foreground text-sm transition-colors duration-200"
                    >
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </footer>
  );
}
