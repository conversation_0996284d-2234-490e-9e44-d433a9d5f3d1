"use server";

import { redirect } from "next/navigation";

import { auth } from "@/auth";
import { DEFAULT_LOGIN_REDIRECT } from "@/routes";

export default async function InitiateGoogleSignIn() {
  const result = await auth.api.signInSocial({
    body: {
      callbackURL: DEFAULT_LOGIN_REDIRECT,
      errorCallbackURL: "/auth/error",
      provider: "google",
    },
  });

  if ("url" in result && result.redirect) {
    redirect(result.url as string);
  }

  return { success: "Successfully initiated Google sign-in", result };
}
