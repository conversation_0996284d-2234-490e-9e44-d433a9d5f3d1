import GetAuthSession from "@/action/auth/get-auth-session";
import FooterSection from "@/components/root/footer-section";
import TopNav from "@/components/root/top-nav";

export default async function BaseLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const session = await GetAuthSession();

  return (
    <>
      <TopNav session={session} />

      {children}

      <FooterSection />
    </>
  );
}
