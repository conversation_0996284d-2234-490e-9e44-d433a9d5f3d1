export const comparisonFeatures = [
  {
    category: "Core Features",
    features: [
      {
        name: "Unlimited members",
        free: true,
        basic: true,
        business: true,
        enterprise: true,
      },
      {
        name: "File uploads",
        free: "10MB",
        basic: "Unlimited",
        business: "Unlimited",
        enterprise: "Unlimited",
      },
      {
        name: "Issues",
        free: "250",
        basic: "Unlimited",
        business: "Unlimited",
        enterprise: "Unlimited",
      },
      {
        name: "Teams",
        free: "2",
        basic: "5",
        business: "Unlimited",
        enterprise: "Unlimited",
      },
    ],
  },
  {
    category: "Authentication & Security",
    features: [
      {
        name: "Google SSO",
        free: true,
        basic: true,
        business: true,
        enterprise: true,
      },
      {
        name: "Admin roles",
        free: false,
        basic: true,
        business: true,
        enterprise: true,
      },
      {
        name: "SAML",
        free: false,
        basic: false,
        business: false,
        enterprise: true,
      },
      {
        name: "Advanced security",
        free: false,
        basic: false,
        business: false,
        enterprise: true,
      },
    ],
  },
  {
    category: "Advanced Features",
    features: [
      {
        name: "Next Core Asks",
        free: false,
        basic: false,
        business: true,
        enterprise: true,
      },
      {
        name: "Next Core Insights",
        free: false,
        basic: false,
        business: true,
        enterprise: true,
      },
      {
        name: "Issue SLAs",
        free: false,
        basic: false,
        business: false,
        enterprise: true,
      },
      {
        name: "Private teams",
        free: false,
        basic: false,
        business: true,
        enterprise: true,
      },
    ],
  },
];
