"use client";

import { CheckCircle } from "lucide-react";
import { motion } from "motion/react";

interface SuccessMessageProps {
  message: string;
}

export default function SuccessMessage({ message }: SuccessMessageProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className="flex items-center gap-2 rounded-md border border-green-200 bg-green-50 p-4 text-sm text-green-800 dark:border-green-800/20 dark:bg-green-900/10 dark:text-green-400"
    >
      <CheckCircle className="h-4 w-4 shrink-0" />
      <span>{message}</span>
    </motion.div>
  );
}
