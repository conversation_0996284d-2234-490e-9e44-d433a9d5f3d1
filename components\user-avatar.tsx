"use client";

import { useTransition } from "react";

import { useRouter } from "@bprogress/next";
import {
  CircleHelpIcon,
  CreditCardIcon,
  EclipseIcon,
  LogOutIcon,
  MonitorIcon,
  MoonIcon,
  SettingsIcon,
  SunIcon,
  UserIcon,
} from "lucide-react";
import { useTheme } from "next-themes";
import Image from "next/image";
import Link from "next/link";

import SignOut from "@/action/auth/sign-out";
import { Session } from "@/auth";
import ConfirmationDialog from "@/components/confirmation-dialog";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import useDialog from "@/hooks/use-dialog";
import { cn } from "@/lib/utils";

const themes = [
  { id: "light", label: "Light Mode", icon: SunIcon },
  { id: "dark", label: "Dark Mode", icon: MoonIcon },
  { id: "system", label: "System", icon: MonitorIcon },
] as const;

interface UserAvatarProps {
  session: Session;
}

export default function UserAvatar({ session }: UserAvatarProps) {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const {
    open: openSignOutDialog,
    onOpenChange: onOpenChangeSignOutDialog,
    openDialog: openDialogSignOutDialog,
    closeDialog: closeDialogSignOutDialog,
  } = useDialog();

  const { theme, setTheme } = useTheme();

  function handleSignOut() {
    startTransition(async () => {
      try {
        const result = await SignOut();
        if (result.success) {
          router.refresh();
        }
      } catch (error) {
        console.error("Sign out error:", error);
      }
    });
  }

  function getInitials(name: string) {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  }

  return (
    <>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="relative h-8 w-8 truncate rounded-full"
            disabled={isPending}
          >
            {session?.user.image ? (
              <Image
                src={session.user.image ?? "/user-fallback.png"}
                alt={session.user.name ?? "User Image"}
                fill
                priority
                className="object-cover"
                draggable={false}
              />
            ) : (
              <div className="bg-primary text-primary-foreground flex h-8 w-8 items-center justify-center rounded-full text-sm font-medium">
                {getInitials(session?.user.name || "")}
              </div>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className="bg-background/95 border-border/50 w-64 border p-2 shadow-xl backdrop-blur-xl"
          align="end"
          sideOffset={8}
          forceMount
        >
          <DropdownMenuLabel className="bg-muted/30 rounded-lg p-3 font-normal">
            <div className="flex flex-col space-y-1">
              <p className="text-foreground text-sm font-medium">
                {session?.user.name}
              </p>
              <p className="text-muted-foreground truncate text-xs">
                {session?.user.email}
              </p>
            </div>
          </DropdownMenuLabel>

          <DropdownMenuGroup className="my-2">
            <DropdownMenuItem asChild>
              <Link
                href="/dashboard"
                className="hover:bg-muted/50 focus:bg-muted/50 flex cursor-pointer items-center gap-3 truncate !rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200"
              >
                <UserIcon className="text-muted-foreground h-4 w-4" />
                <span>Dashboard</span>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem
              className="hover:bg-muted/50 focus:bg-muted/50 flex cursor-pointer items-center gap-3 truncate !rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200"
              onClick={() => {}}
            >
              <SettingsIcon className="text-muted-foreground h-4 w-4" />
              <span>Settings</span>
            </DropdownMenuItem>
          </DropdownMenuGroup>

          <DropdownMenuSeparator className="bg-border/50 my-2 w-[110%] -translate-x-1" />

          <DropdownMenuGroup>
            <DropdownMenuItem asChild>
              <Link
                href="/help"
                className="hover:bg-muted/50 focus:bg-muted/50 flex cursor-pointer items-center gap-3 truncate !rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200"
              >
                <CircleHelpIcon className="text-muted-foreground h-4 w-4" />
                <span>Help Center</span>
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem className="hover:bg-muted/50 focus:bg-muted/50 flex cursor-pointer items-center gap-3 truncate !rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200">
              <CreditCardIcon className="text-muted-foreground h-4 w-4" />
              <span>Billing</span>
            </DropdownMenuItem>
            <DropdownMenuSub>
              <DropdownMenuSubTrigger className="hover:bg-muted/50 focus:bg-muted/50 flex cursor-pointer items-center gap-3 truncate !rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200">
                <EclipseIcon className="text-muted-foreground h-4 w-4" />
                <span>Appearance</span>
              </DropdownMenuSubTrigger>
              <DropdownMenuPortal>
                <DropdownMenuSubContent className="bg-background/95 border-border/50 w-48 border p-2 shadow-xl backdrop-blur-xl">
                  {themes.map(({ id, label, icon: Icon }) => {
                    const isActive = theme === id;
                    return (
                      <DropdownMenuItem
                        key={id}
                        onClick={() => setTheme(id)}
                        className={cn(
                          "hover:bg-muted/50 focus:bg-muted/50 flex cursor-pointer items-center gap-3 truncate !rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200",
                          isActive && "text-primary",
                        )}
                      >
                        <Icon
                          className={cn(
                            "h-4 w-4 transition-colors",
                            isActive ? "text-primary" : "text-muted-foreground",
                          )}
                        />
                        <span>{label}</span>
                        {isActive && (
                          <div className="bg-primary ml-auto h-1.5 w-1.5 rounded-full" />
                        )}
                      </DropdownMenuItem>
                    );
                  })}
                </DropdownMenuSubContent>
              </DropdownMenuPortal>
            </DropdownMenuSub>
          </DropdownMenuGroup>

          <DropdownMenuSeparator className="bg-border/50 my-2 w-[110%] -translate-x-1" />

          <DropdownMenuItem
            className="hover:bg-destructive/10 focus:bg-destructive/10 focus:text-destructive text-destructive flex cursor-pointer items-center gap-3 truncate !rounded-lg px-3 py-2 text-sm font-medium transition-all duration-200"
            onClick={openDialogSignOutDialog}
            disabled={isPending}
          >
            <LogOutIcon className="text-destructive h-4 w-4" />
            <span>{isPending ? "Signing out..." : "Sign out"}</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <ConfirmationDialog
        open={openSignOutDialog}
        onOpenChange={onOpenChangeSignOutDialog}
        closeDialog={closeDialogSignOutDialog}
        onClick={handleSignOut}
        isPending={isPending}
        title="Are you sure you want to sign out?"
        description="This will log you out of your account"
        confirmText="Confirm"
        confirmVariant="destructive"
      />
    </>
  );
}
