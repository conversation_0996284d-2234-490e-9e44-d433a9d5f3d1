"use client";

import {
  <PERSON><PERSON><PERSON>za,
  CredenzaBody,
  CredenzaClose,
  CredenzaContent,
  CredenzaDescription,
  CredenzaFooter,
  CredenzaHeader,
  CredenzaTitle,
} from "@/components/custom/ui/credenza";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ConfirmationDialogProps {
  open: boolean;
  onOpenChange?: (open: boolean) => void;
  closeDialog?: () => void;
  title?: string;
  description?: string;
  children?: Readonly<React.ReactNode>;
  onClick?: () => void;
  isPending?: boolean;
  className?: string;
  confirmText?: string;
  confirmVariant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  cancelText?: string;
  cancelVariant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
}

export default function ConfirmationDialog({
  open,
  onOpenChange,
  closeDialog,
  title,
  description,
  children,
  onClick,
  isPending,
  className,
  confirmText = "Confirm",
  confirmVariant = "default",
  cancelText = "Cancel",
  cancelVariant = "outline",
}: ConfirmationDialogProps) {
  return (
    <Credenza
      open={open}
      onOpenChange={onOpenChange}
    >
      <CredenzaContent
        className={cn(
          isPending
            ? "[&>button]:text-muted-foreground [&>button]:pointer-events-none [&>button]:opacity-50"
            : "",
          className,
        )}
      >
        <CredenzaHeader className="space-y-1 pb-2">
          <CredenzaTitle>{title}</CredenzaTitle>
          <CredenzaDescription>{description}</CredenzaDescription>
        </CredenzaHeader>

        <CredenzaBody className="py-1">{children}</CredenzaBody>

        <CredenzaFooter className="gap-3 pt-2">
          <CredenzaClose asChild>
            <Button
              variant={cancelVariant}
              disabled={isPending}
              onClick={closeDialog}
            >
              {cancelText}
            </Button>
          </CredenzaClose>
          <Button
            variant={confirmVariant}
            className={cn(
              "flex-none",
              confirmVariant === "destructive" ? "text-white" : "",
            )}
            onClick={onClick || closeDialog}
            disabled={isPending}
          >
            {isPending ? "Processing..." : confirmText}
          </Button>
        </CredenzaFooter>
      </CredenzaContent>
    </Credenza>
  );
}
