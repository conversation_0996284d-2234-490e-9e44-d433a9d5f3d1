"use client";

import { ArrowRightIcon } from "lucide-react";
import { motion } from "motion/react";

const features = [
  {
    title: "Build momentum with Cycles",
    description:
      "Create healthy routines and focus your team on what work should happen next.",
    visual: (
      <div className="relative h-64 w-full overflow-hidden rounded-lg bg-gradient-to-br from-slate-900 to-slate-800">
        {/* Cycles chart visualization */}
        <div className="absolute inset-0 p-6">
          <div className="mb-4">
            <div className="mb-2 text-xs text-slate-400">Cycles</div>
            <div className="flex gap-4 text-xs">
              <span className="flex items-center gap-1">
                <div className="h-2 w-2 rounded-full bg-blue-400"></div>
                Active
              </span>
              <span className="flex items-center gap-1">
                <div className="h-2 w-2 rounded-full bg-green-400"></div>
                Started
              </span>
              <span className="flex items-center gap-1">
                <div className="h-2 w-2 rounded-full bg-gray-400"></div>
                Completed
              </span>
            </div>
          </div>
          <div className="relative h-32">
            <svg
              className="h-full w-full"
              viewBox="0 0 300 120"
            >
              <path
                d="M 20 100 Q 80 60 140 80 T 280 40"
                stroke="#3b82f6"
                strokeWidth="2"
                fill="none"
                className="opacity-80"
              />
              <circle
                cx="140"
                cy="80"
                r="3"
                fill="#3b82f6"
              />
              <circle
                cx="220"
                cy="60"
                r="3"
                fill="#10b981"
              />
            </svg>
          </div>
        </div>
      </div>
    ),
  },
  {
    title: "Manage incoming work with Triage",
    description:
      "Review and assign incoming bug reports, feature requests, and other unplanned work.",
    visual: (
      <div className="relative h-64 w-full overflow-hidden rounded-lg bg-gradient-to-br from-slate-900 to-slate-800">
        {/* Triage interface mockup */}
        <div className="absolute inset-0 p-6">
          <div className="mb-4">
            <div className="mb-2 text-xs text-slate-400">Triage</div>
          </div>
          <div className="space-y-2">
            <div className="rounded border border-slate-700/50 bg-slate-800/50 p-3">
              <div className="mb-1 text-xs text-slate-300">
                Users report unexpected rate limiting
              </div>
              <div className="flex gap-2 text-xs">
                <span className="rounded bg-slate-700 px-2 py-1 text-slate-300">
                  High
                </span>
                <span className="text-slate-500">Accept</span>
              </div>
            </div>
            <div className="rounded border border-slate-700/30 bg-slate-800/30 p-3">
              <div className="mb-1 text-xs text-slate-400">
                Mark as duplicate
              </div>
              <div className="flex gap-2 text-xs">
                <span className="text-slate-500">Decline</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    ),
  },
  {
    title: "Custom views",
    description:
      "Switch between list and board views to see your work in the way that's most relevant to you.",
    visual: (
      <div className="relative h-64 w-full overflow-hidden rounded-lg bg-gradient-to-br from-slate-900 to-slate-800">
        {/* Custom views interface */}
        <div className="absolute inset-0 p-6">
          <div className="mb-4">
            <div className="mb-2 text-xs text-slate-400">Views</div>
            <div className="flex gap-2 text-xs">
              <span className="rounded bg-slate-700 px-2 py-1 text-slate-300">
                List
              </span>
              <span className="text-slate-500">Board</span>
            </div>
          </div>
          <div className="space-y-2">
            <div className="rounded border border-slate-700/50 bg-slate-800/50 p-2">
              <div className="text-xs text-slate-300">Tailored workflows</div>
            </div>
            <div className="rounded border border-slate-700/50 bg-slate-800/50 p-2">
              <div className="text-xs text-slate-300">Custom views</div>
            </div>
            <div className="rounded border border-slate-700/30 bg-slate-800/30 p-2">
              <div className="text-xs text-slate-400">Filters</div>
            </div>
          </div>
        </div>
      </div>
    ),
  },
];

export default function FeaturesSection() {
  return (
    <section className="bg-muted/30 relative px-4 py-24 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        {/* Section Header - Keep existing design */}
        <div className="mb-16 grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <h2 className="from-primary via-primary/80 to-primary/70 bg-gradient-to-r bg-clip-text text-5xl leading-tight font-medium text-transparent">
              Made for modern product teams
            </h2>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true, amount: 0.1 }}
            transition={{ duration: 0.6, ease: "easeOut", delay: 0.1 }}
            className="space-y-4"
          >
            <p className="text-muted-foreground text-lg leading-relaxed font-normal">
              Next Core is shaped by the practices and principles that
              distinguish world-class product teams from the rest: relentless
              focus, fast execution, and a commitment to the quality of craft.
            </p>
            <div className="flex items-center">
              <span className="text-primary flex cursor-pointer items-center gap-2 text-sm font-medium hover:underline">
                Make the switch <ArrowRightIcon className="h-3 w-3" />
              </span>
            </div>
          </motion.div>
        </div>

        {/* Linear-style Feature Grid with Borders */}
        <div className="grid grid-cols-1 border-t lg:grid-cols-2">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{
                once: true,
                amount: 0.2,
                margin: "0px 0px -100px 0px",
              }}
              transition={{
                duration: 0.8,
                ease: [0.25, 0.1, 0.25, 1],
                delay: index * 0.15,
              }}
              className={`group relative border-b p-12 transition-all duration-300 ${
                index % 2 === 1 ? "lg:border-l" : ""
              }`}
            >
              {/* Visual Section */}
              <div className="mb-8">{feature.visual}</div>

              {/* Content Section */}
              <div className="space-y-4">
                <h3 className="text-foreground text-2xl leading-tight font-semibold">
                  {feature.title}
                </h3>
                <p className="text-muted-foreground text-base leading-relaxed font-normal">
                  {feature.description}
                </p>
              </div>

              {/* Subtle hover effect */}
              <div className="absolute inset-0 bg-gradient-to-t from-transparent to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-5"></div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  );
}
