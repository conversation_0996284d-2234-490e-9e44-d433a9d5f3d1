import type { Metada<PERSON> } from "next";

import "@/app/globals.css";
import Providers from "@/components/providers";
import { DMSans, GeistMono, PlusJakartaSans } from "@/config/fonts";
import { cn } from "@/lib/utils";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html
      lang="en"
      suppressHydrationWarning
    >
      <body
        className={cn(
          "!font-plus-jakarta-sans !scroll-smooth !antialiased",
          PlusJakartaSans.variable,
          GeistMono.variable,
          DMSans.variable,
        )}
      >
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
