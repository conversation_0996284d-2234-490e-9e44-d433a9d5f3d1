"use client";

import { AlertCircle } from "lucide-react";
import { motion } from "motion/react";

interface ErrorMessageProps {
  error: string;
}

export default function ErrorMessage({ error }: ErrorMessageProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: -10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.3, ease: "easeOut" }}
      className="border-destructive/20 bg-destructive/10 text-destructive flex items-center gap-2 rounded-md border p-4 text-sm"
    >
      <AlertCircle className="h-4 w-4 shrink-0" />
      <span>{error}</span>
    </motion.div>
  );
}
