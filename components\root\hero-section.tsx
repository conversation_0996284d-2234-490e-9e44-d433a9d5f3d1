"use client";

import { ArrowRightIcon } from "lucide-react";
import { motion } from "motion/react";

import AlgoliaBlueButton from "@/components/custom/ui/algolia-blue-button";
import { MacbookMockUp } from "@/components/custom/ui/macbook-mock-up";
import { PointerHighlight } from "@/components/custom/ui/pointer-highlight";

export default function HeroSection() {
  return (
    <section className="relative px-4 py-32 sm:px-6 lg:px-8 lg:py-40">
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#4f4f4f2e_1px,transparent_1px),linear-gradient(to_bottom,#4f4f4f2e_1px,transparent_1px)] [mask-image:radial-gradient(ellipse_60%_50%_at_50%_0%,#000_70%,transparent_100%)] bg-[size:35px_34px]" />

      <div className="mx-auto max-w-6xl">
        {/* Content - Centered Layout like Linear */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="text-center"
        >
          {/* Main Heading - Linear Style */}
          <h1 className="from-primary via-primary/80 to-primary/70 mb-6 bg-gradient-to-r bg-clip-text text-3xl leading-tight font-medium tracking-tight text-transparent sm:text-4xl lg:text-6xl">
            Next Core is a
            <PointerHighlight
              rectangleClassName="bg-sky-100/10 dark:bg-sky-900/10 border-sky-300 dark:border-sky-700 leading-loose "
              pointerClassName="text-sky-500 h-4 w-4"
              containerClassName="inline-block mx-1"
              animateOnce={true}
            >
              <span className="text-primary/80">purpose-built tool</span>
            </PointerHighlight>
            for
            <br className="hidden sm:block" />
            <span className="sm:hidden"> </span>planning and building products
          </h1>

          {/* Description - Linear Style */}
          <p className="text-muted-foreground mx-auto mb-8 max-w-2xl text-base leading-relaxed sm:text-lg">
            Meet the complete Next.js boilerplate for modern SaaS development.
            <br className="hidden sm:block" />
            <span className="sm:hidden"> </span>Streamline authentication,
            payments, and product launches.
          </p>

          {/* CTA Buttons - Linear Style */}
          <div className="mb-16 flex flex-col gap-3 sm:flex-row sm:justify-center sm:gap-4">
            <AlgoliaBlueButton>Start Building</AlgoliaBlueButton>

            <button className="text-muted-foreground hover:text-foreground flex cursor-pointer items-center justify-center gap-2 text-sm font-medium transition-colors">
              Introducing Next Core for Agents
              <ArrowRightIcon className="h-4 w-4" />
            </button>
          </div>
        </motion.div>

        {/* Dashboard Mockup - Linear Style */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
          className="relative"
        >
          <div className="relative mx-auto max-w-6xl content-center">
            {/* Linear-style Dashboard Interface */}
            <MacbookMockUp
              src="https://devicescss.xyz/assets/img/bg-10.jpg"
              className="mx-auto"
            />
          </div>
        </motion.div>
      </div>
    </section>
  );
}
