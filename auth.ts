import {
  checkout,
  polar as polarPlugin,
  portal,
  usage,
} from "@polar-sh/better-auth";
import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { admin, magicLink } from "better-auth/plugins";

import SendMagicLinkEmail from "@/action/auth/send-magic-link-email";
import polarClient from "@/lib/polar";
import prisma from "@/lib/prisma";

export const auth = betterAuth({
  database: prismaAdapter(prisma, {
    provider: "mysql",
  }),

  appUrl: process.env.BETTER_AUTH_URL as string,
  secret: process.env.BETTE_AUTH_SECRET as string,

  socialProviders: {
    google: {
      clientId: process.env.GOOGLE_CLIENT_ID as string,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
    },
  },

  account: {
    accountLinking: {
      enabled: true,
      trustedProviders: ["google"],
    },
  },

  plugins: [
    admin(),

    magicLink({
      sendMagicLink: async ({ email, url }) => {
        const result = await SendMagicLinkEmail({ email, url });

        if (!result.success) {
          throw new Error(result.error || "Failed to send magic link email");
        }
      },
    }),

    polarPlugin({
      client: polarClient,
      createCustomerOnSignUp: true,
      use: [
        checkout({
          authenticatedUsersOnly: true,
          successUrl: "/pricing",
        }),
        portal(),
        usage(),
      ],
    }),
  ],

  onAPIError: {
    throw: true,
    onError(error, ctx) {
      console.error("API Error:", error, ctx);
    },
    errorURL: "/auth/sign-in",
  },

  trustedOrigins: [
    "localhost:3000", // Trust localhost:3000 (any protocol)
    "*.vercel.app", // Trust all subdomains of vercel.app (any protocol)
  ],
});

export type Session = Awaited<ReturnType<typeof auth.api.getSession>>;
