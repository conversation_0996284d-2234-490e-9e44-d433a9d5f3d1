const apiAuthPrefix = "/api/auth";
const publicRoutes = [
  "/",
  "/terms",
  "/privacy",
  "/features",
  "/docs",
  "/company",
];
const authRoutes = ["/auth/sign-in"];

// Helper function to check if a route is public (supports patterns)
const isPublicRoute = (pathname: string): boolean => {
  return (
    publicRoutes.includes(pathname) ||
    publicRoutes.some((route) => {
      // Support wildcard patterns like "/blog/*"
      if (route.endsWith("/*")) {
        return pathname.startsWith(route.slice(0, -2));
      }
      return false;
    })
  );
};

const adminRoute = "/admin";
const onboardingRoute = "/onboarding";
const pendingApprovalRoute = "/pending-approval";

const SIGN_IN_REDIRECT = "/auth/sign-in";
const DEFAULT_LOGIN_REDIRECT = "/dashboard";

export {
  apiAuthPrefix,
  publicRoutes,
  authRoutes,
  adminRoute,
  onboardingRoute,
  pendingApprovalRoute,
  SIGN_IN_REDIRECT,
  DEFAULT_LOGIN_REDIRECT,
  isPublicRoute,
};
