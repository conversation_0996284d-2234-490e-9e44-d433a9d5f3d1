import * as React from "react";

export function useMediaQuery(query: string) {
  const [value, setValue] = React.useState(false);
  const [hasMounted, setHasMounted] = React.useState(false);

  React.useEffect(() => {
    setHasMounted(true);

    function onChange(event: MediaQueryListEvent) {
      setValue(event.matches);
    }

    const result = matchMedia(query);
    result.addEventListener("change", onChange);
    setValue(result.matches);

    return () => result.removeEventListener("change", onChange);
  }, [query]);

  // Return false during SSR and initial hydration to prevent mismatch
  if (!hasMounted) {
    return false;
  }

  return value;
}
