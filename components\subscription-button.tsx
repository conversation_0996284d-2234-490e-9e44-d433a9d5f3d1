"use client";

import { useTransition } from "react";

import { useRouter } from "@bprogress/next";

import { Session } from "@/auth";
import { Button } from "@/components/ui/button";
import { Plan } from "@/config/plans";
import { cn } from "@/lib/utils";

interface SubscriptionButtonProps {
  plan: Plan;
  session: Session;
}

export default function SubscriptionButton({
  plan,
  session,
}: SubscriptionButtonProps) {
  const [isPending, startTransition] = useTransition();
  const router = useRouter();

  function handleSubscription() {
    startTransition(async () => {
      if (!session) {
        router.push("/dashboard");
        return;
      }

      if (plan.name === "basic") {
        router.push("/dashboard");
        return;
      }

      if (plan.name === "enterprise") {
        router.push("/contact");
        return;
      }

      try {
        console.log("Test");
      } catch (error) {
        console.error(error);
      }
    });
  }

  return (
    <Button
      variant={plan.buttonVariant}
      className={cn(
        "w-full font-medium",
        plan.popular &&
          "bg-primary text-primary-foreground hover:bg-primary/90",
      )}
      onClick={handleSubscription}
      disabled={isPending}
    >
      {plan.buttonText}
    </Button>
  );
}
