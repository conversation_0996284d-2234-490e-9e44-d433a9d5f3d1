import { CSSProperties } from "react";

import { cookies } from "next/headers";

import GetAuthSession from "@/action/auth/get-auth-session";
import AppSidebar from "@/components/sidebar/app-sidebar";
import ClientSidebarLayout from "@/components/sidebar/client-sidebar-layout";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";

const SIDEBAR_COOKIE_NAME = "sidebar_state";

export default async function SidebarLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const cookieStore = await cookies();
  const defaultOpen = cookieStore.get(SIDEBAR_COOKIE_NAME)?.value === "true";

  const session = await GetAuthSession();

  return (
    <SidebarProvider
      defaultOpen={defaultOpen}
      style={
        {
          "--sidebar-width": "300px",
        } as CSSProperties
      }
    >
      <AppSidebar collapsible="icon" />
      <SidebarInset className="flex h-screen flex-col overflow-hidden">
        <ClientSidebarLayout session={session}>{children}</ClientSidebarLayout>
      </SidebarInset>
    </SidebarProvider>
  );
}
