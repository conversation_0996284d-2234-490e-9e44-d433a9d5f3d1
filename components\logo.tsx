import Link from "next/link";

import { cn } from "@/lib/utils";

function LogoIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      fill="none"
      width="30"
      height="30"
      viewBox="0 0 100 100"
    >
      <g fill="var(--primary)">
        {/* Top circle */}
        <circle
          cx="50"
          cy="25"
          r="15"
          opacity="0.9"
        />
        {/* Right circle */}
        <circle
          cx="75"
          cy="50"
          r="15"
          opacity="0.9"
        />
        {/* Bottom circle */}
        <circle
          cx="50"
          cy="75"
          r="15"
          opacity="0.9"
        />
        {/* Left circle */}
        <circle
          cx="25"
          cy="50"
          r="15"
          opacity="0.9"
        />

        {/* Center overlapping area - creates the interlocking effect */}
        <circle
          cx="50"
          cy="50"
          r="8"
          fill="var(--primary)"
        />

        {/* Cutout circles to create the interlocking pattern */}
        <circle
          cx="50"
          cy="35"
          r="6"
          fill="var(--background)"
        />
        <circle
          cx="65"
          cy="50"
          r="6"
          fill="var(--background)"
        />
        <circle
          cx="50"
          cy="65"
          r="6"
          fill="var(--background)"
        />
        <circle
          cx="35"
          cy="50"
          r="6"
          fill="var(--background)"
        />
      </g>
    </svg>
  );
}

interface LogoProps {
  className?: string;
  withText?: boolean;
}

export default function Logo({ className, withText }: LogoProps) {
  return (
    <Link
      href="/"
      className={cn("flex items-center space-x-2", className)}
    >
      <LogoIcon />

      {withText ? (
        <span className="text-foreground/90 !font-dm-sans text-xl font-medium tracking-tighter capitalize select-none">
          Next Core
        </span>
      ) : null}
    </Link>
  );
}
