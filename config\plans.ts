export const plans = [
  {
    name: "basic",
    priceId: undefined,
    limits: {
      tokens: 100,
    },
    price: "$0",
    description: "Free for everyone",
    features: [
      "All Free features +",
      "5 teams",
      "Unlimited issues",
      "Unlimited file uploads",
      "Admin roles",
    ],
    buttonText: "Get started",
    buttonVariant: "outline" as const,
    popular: false,
  },
  {
    name: "pro",
    priceId: "price_1RdbaRC2esG5jHKZWsmrCjyl",
    limits: {
      tokens: 500,
    },
    price: "$8",
    period: "per user/month",
    billing: "Billed yearly",
    description: "For growing teams",
    features: [
      "All Free features +",
      "5 teams",
      "Unlimited issues",
      "Unlimited file uploads",
      "Admin roles",
    ],
    buttonText: "Get started",
    buttonVariant: "outline" as const,
    popular: false,
  },
  {
    name: "business",
    priceId: "price_1RdbbOC2esG5jHKZb2LNdaRf",
    limits: {
      tokens: 1000,
    },
    price: "$14",
    period: "per user/month",
    billing: "Billed yearly",
    description: "For established teams",
    features: [
      "All Basic features +",
      "Next Core Asks",
      "Unlimited teams",
      "Private teams and guests",
      "Next Core Insights",
      "Triage responsibility",
    ],
    buttonText: "Get started",
    buttonVariant: "default" as const,
    popular: true,
  },
  {
    name: "enterprise",
    priceId: undefined,
    limits: {
      tokens: 1000,
    },
    price: "Contact us",
    billing: "Annual billing only",
    description: "For large organizations",
    features: [
      "All Business features +",
      "Advanced Next Core Asks",
      "Issue SLAs",
      "SAML and SCIM",
      "Advanced security",
      "Migration and onboarding support",
    ],
    buttonText: "Request trial",
    buttonVariant: "outline" as const,
    popular: false,
  },
];

export type Plan = (typeof plans)[number];
