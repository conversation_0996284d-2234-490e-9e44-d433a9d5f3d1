import localFont from "next/font/local";

const PlusJakartaSans = localFont({
  src: "../fonts/PlusJakartaSans-VariableFont_wght.ttf",
  variable: "--font-plus-jakarta-sans",
  display: "swap",
  adjustFontFallback: false,
});

const GeistMono = localFont({
  src: "../fonts/GeistMono-VariableFont_wght.ttf",
  variable: "--font-geist-mono",
  display: "swap",
  adjustFontFallback: false,
});

const DMSans = localFont({
  src: "../fonts/DMSans-VariableFont_opsz,wght.ttf",
  variable: "--font-dm-sans",
  display: "swap",
  preload: true,
  adjustFontFallback: false,
});

export { PlusJakartaSans, GeistMono, DMSans };
