"use client";

import { useEffect, useState, useTransition } from "react";

import { zodResolver } from "@hookform/resolvers/zod";
import { ArrowLeftIcon } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import InitiateGoogleSignIn from "@/action/auth/initiate-google-sign-in";
import { authClient } from "@/auth-client";
import ErrorMessage from "@/components/auth/error-message";
import SuccessMessage from "@/components/auth/success-message";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

const emailSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
});

type EmailFormData = z.infer<typeof emailSchema>;

export default function SignInForm() {
  const [isPending, startTransition] = useTransition();

  const [showEmailForm, setShowEmailForm] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const form = useForm<EmailFormData>({
    resolver: zodResolver(emailSchema),
    defaultValues: {
      email: "",
    },
  });

  // Auto-clear success message after 5 seconds
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        setSuccess(null);
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [success]);

  function clearErrors() {
    setError(null);
    setSuccess(null);
  }

  function handleGoogleAuth() {
    startTransition(async () => {
      try {
        clearErrors();

        await InitiateGoogleSignIn();
      } catch {
        setError("Failed to initiate Google sign-in. Please try again.");
      }
    });
  }

  function handleMagicLink(email: string) {
    startTransition(async () => {
      try {
        clearErrors();

        const { error } = await authClient.signIn.magicLink({
          email,
          callbackURL: "/dashboard",
        });

        if (error) {
          setError(error.message || "Failed to send magic link");
          return;
        }

        setSuccess("Magic link sent! Check your email.");
        form.reset();
      } catch {
        setError("Failed to send magic link. Please try again.");
      }
    });
  }

  return (
    <AnimatePresence mode="wait">
      {!showEmailForm ? (
        <motion.div
          key="auth-options"
          className="mx-auto w-full max-w-sm space-y-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.4, ease: "easeOut" }}
        >
          {/* Title */}
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1, ease: "easeOut" }}
          >
            <h1 className="text-foreground text-xl font-semibold tracking-tight">
              Sign in to your account
            </h1>
          </motion.div>

          {/* Error Message */}
          <AnimatePresence mode="wait">
            {error && <ErrorMessage error={error} />}
          </AnimatePresence>

          {/* Auth Options */}
          <motion.div
            className="space-y-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2, ease: "easeOut" }}
          >
            {/* Google OAuth Button */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.2, delay: 0.3, ease: "easeOut" }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                className="hover:bg-accent/50 from-primary to-primary/85 text-primary-foreground h-12 w-full transform-gpu border border-zinc-950/25 bg-gradient-to-t font-medium shadow-md ring-1 shadow-zinc-950/20 ring-white/20 transition-[filter] duration-200 ring-inset hover:brightness-110 active:brightness-90 dark:border-white/20 dark:ring-transparent"
                onClick={handleGoogleAuth}
                disabled={isPending}
              >
                <svg
                  className="mr-0.5 h-5 w-5"
                  viewBox="0 0 24 24"
                >
                  <path
                    fill="currentColor"
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  />
                  <path
                    fill="currentColor"
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  />
                  <path
                    fill="currentColor"
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  />
                  <path
                    fill="currentColor"
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  />
                </svg>
                Continue with Google
              </Button>
            </motion.div>

            {/* Email Button */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.2, delay: 0.4, ease: "easeOut" }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                variant="outline"
                className="text-foreground border-border hover:bg-accent/50 h-12 w-full transform-gpu font-medium transition-all duration-200"
                onClick={() => {
                  clearErrors();
                  setShowEmailForm(true);
                }}
                disabled={isPending}
              >
                Continue with email
              </Button>
            </motion.div>
          </motion.div>
        </motion.div>
      ) : (
        <motion.div
          key="email-form"
          className="mx-auto w-full max-w-sm space-y-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.4, ease: "easeOut" }}
        >
          <motion.div
            className="text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1, ease: "easeOut" }}
          >
            <h1 className="text-foreground text-xl font-semibold tracking-tight">
              What&apos;s your email address?
            </h1>
          </motion.div>

          {/* Success Message */}
          <AnimatePresence mode="wait">
            {success && <SuccessMessage message={success} />}
          </AnimatePresence>

          {/* Error Message */}
          <AnimatePresence mode="wait">
            {error && <ErrorMessage error={error} />}
          </AnimatePresence>

          {/* Email Form */}
          <motion.div
            className="space-y-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.2, ease: "easeOut" }}
          >
            <Form {...form}>
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  const email = form.getValues("email");
                  if (email) {
                    handleMagicLink(email);
                  }
                }}
                className="space-y-4"
              >
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.3, ease: "easeOut" }}
                >
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem className="space-y-2">
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="Enter your email address..."
                            disabled={isPending}
                            className="bg-background/60 border-border/60 focus:border-primary focus:ring-primary/15 hover:border-border focus:bg-background h-12 transform-gpu text-center transition-all duration-200"
                            required
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3, delay: 0.4, ease: "easeOut" }}
                  whileTap={{ scale: 0.98 }}
                >
                  <Button
                    type="submit"
                    className="hover:bg-primary/90 h-11 w-full transform-gpu font-medium transition-all duration-200"
                    disabled={isPending || !!success}
                  >
                    {isPending ? (
                      <>
                        <motion.div
                          className="mr-2 h-4 w-4 rounded-full border-2 border-current border-t-transparent"
                          animate={{ rotate: 360 }}
                          transition={{
                            duration: 1,
                            repeat: Infinity,
                            ease: "linear",
                          }}
                        />
                        Sending magic link...
                      </>
                    ) : success ? (
                      "Magic link sent!"
                    ) : (
                      "Continue with email"
                    )}
                  </Button>
                </motion.div>
              </form>
            </Form>

            {/* Back to login link */}
            <motion.div
              className="text-center"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.5, ease: "easeOut" }}
            >
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  clearErrors();
                  setShowEmailForm(false);
                }}
                className="mt-2"
              >
                <ArrowLeftIcon className="mr-2 h-4 w-4" />
                Back to options
              </Button>
            </motion.div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
