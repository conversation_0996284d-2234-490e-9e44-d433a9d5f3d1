"use client";

import { useRef, useState } from "react";

import { ArrowRightIcon, ChevronDownIcon, SearchIcon } from "lucide-react";
import { AnimatePresence, motion } from "motion/react";

export default function HelpPage() {
  // State for accordion
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  // Type-safe useRef for the search input
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Toggle accordion
  const toggleAccordion = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="relative px-4 py-32 sm:px-6 lg:px-8 lg:py-40">
      <div className="mx-auto max-w-6xl">
        {/* Header Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="mb-12 text-center"
        >
          <h1 className="from-primary via-primary/80 to-primary/70 mb-4 bg-gradient-to-r bg-clip-text text-3xl leading-tight font-medium tracking-tight text-transparent sm:text-4xl lg:text-5xl">
            Help Center
          </h1>
          <p className="text-muted-foreground mx-auto max-w-2xl text-base leading-relaxed sm:text-lg">
            Find answers, guides, and support for all your questions.
          </p>
        </motion.div>

        {/* Search Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
          className="mb-12"
        >
          <div className="relative mx-auto max-w-3xl">
            <div className="border-border bg-input flex items-center rounded-lg border px-4 py-3">
              <SearchIcon className="mr-3 h-5 w-5 text-zinc-400" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder="Search for help articles..."
                className="w-full bg-transparent text-sm focus:outline-none sm:text-base"
              />
            </div>
          </div>
        </motion.div>

        {/* Help Categories */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.4 }}
          className="mb-16 grid gap-2 sm:grid-cols-2 lg:grid-cols-3"
        >
          {[
            {
              title: "Getting Started",
              desc: "Learn the basics and set up your account.",
            },
            { title: "Troubleshooting", desc: "Fix common issues and errors." },
            {
              title: "Advanced Features",
              desc: "Explore powerful tools and settings.",
            },
            {
              title: "Billing & Payments",
              desc: "Manage subscriptions and payments.",
            },
            {
              title: "Account Management",
              desc: "Update your profile and preferences.",
            },
            {
              title: "API & Integrations",
              desc: "Connect with other tools and services.",
            },
          ].map((category, index) => (
            <div
              key={index}
              className="bg-card rounded-lg border p-6 shadow-sm transition-colors"
            >
              <h3 className="mb-2 text-lg font-medium">{category.title}</h3>
              <p className="text-muted-foreground mb-4 text-sm">
                {category.desc}
              </p>
              <button className="text-primary hover:text-primary/80 flex cursor-pointer items-center gap-2 text-sm font-medium transition-colors hover:bg-transparent">
                Explore Topics
                <ArrowRightIcon className="h-4 w-4" />
              </button>
            </div>
          ))}
        </motion.div>

        {/* FAQ Section with Accordion */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.6 }}
          className="mb-16"
        >
          <h2 className="mb-6 text-center text-2xl font-medium">
            Frequently Asked Questions
          </h2>
          <div className="space-y-2">
            {[
              {
                q: "How do I reset my password?",
                a: "Follow the 'Forgot Password' link on the login page to receive a reset link via email.",
              },
              {
                q: "Can I change my subscription plan?",
                a: "Yes, visit the Billing section in your account settings to upgrade or downgrade your plan.",
              },
              {
                q: "How do I contact support?",
                a: "Use the Contact Support button below or email <NAME_EMAIL> for assistance.",
              },
            ].map((faq, index) => (
              <div
                key={index}
                className="border-b"
              >
                <button
                  onClick={() => toggleAccordion(index)}
                  className="flex w-full items-center justify-between p-6 text-left"
                >
                  <h3 className="text-2xl font-medium sm:text-3xl">{faq.q}</h3>
                  <ChevronDownIcon
                    className={`text-muted-foreground h-5 w-5 transition-transform duration-300 ${
                      openIndex === index ? "rotate-180" : ""
                    }`}
                  />
                </button>
                <AnimatePresence>
                  {openIndex === index && (
                    <motion.div
                      initial={{ height: 0, opacity: 0 }}
                      animate={{ height: "auto", opacity: 1 }}
                      exit={{ height: 0, opacity: 0 }}
                      transition={{ duration: 0.3, ease: "easeOut" }}
                      className="overflow-hidden"
                    >
                      <p className="text-muted-foreground p-4 pt-0 text-sm">
                        {faq.a}
                      </p>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ))}
          </div>
        </motion.div>

        {/* Contact Support CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.8 }}
          className="text-center"
        >
          <h2 className="mb-4 text-2xl font-medium">Still Need Help?</h2>
          <p className="text-muted-foreground mx-auto mb-6 max-w-2xl text-base">
            Our support team is here to assist you 24/7.
          </p>
          <button className="bg-primary text-primary-foreground hover:bg-primary/90 cursor-pointer rounded-md px-6 py-3 text-sm font-medium transition-colors">
            Contact Support
          </button>
        </motion.div>
      </div>
    </section>
  );
}
