"use client";

import { RiCheckboxCircleFill } from "@remixicon/react";
import { CheckIcon, ChevronRightIcon, MinusIcon } from "lucide-react";
import { motion } from "motion/react";

import { Button } from "@/components/ui/button";
import { comparisonFeatures } from "@/config/comparison-features";
import { plans } from "@/config/plans";
import { cn } from "@/lib/utils";

export default function PricingSection() {
  return (
    <section className="relative px-4 py-24 sm:px-6 lg:px-8">
      <div className="mx-auto max-w-7xl">
        {/* Section Header - Linear Style */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="mb-16"
        >
          {/* Category Label with Green Dot */}
          <div className="mb-8 flex items-center gap-2">
            <div className="h-2.5 w-5 animate-pulse rounded-full bg-green-500/80" />
            <span className="text-muted-foreground text-sm font-medium">
              Pricing and plans
            </span>
            <ChevronRightIcon className="text-muted-foreground h-4 w-4" />
          </div>

          {/* Main Heading */}
          <h2 className="from-primary via-primary/80 to-primary/70 mb-8 bg-gradient-to-r bg-clip-text text-5xl leading-tight font-medium text-transparent lg:text-6xl">
            Simple, transparent pricing
          </h2>

          {/* Description */}
          <div className="max-w-2xl">
            <p className="text-muted-foreground text-lg leading-relaxed">
              <span className="text-foreground font-medium">
                Use Next Core for free with your whole team.
              </span>{" "}
              Upgrade to enable unlimited issues, enhanced security controls,
              and additional features with our flexible pricing plans.
            </p>
          </div>
        </motion.div>

        {/* Linear-style Pricing Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.2 }}
          className="mt-16"
        >
          <div className="bg-card border-border overflow-hidden rounded-lg border">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
              {plans.map((plan, index) => (
                <div
                  key={plan.name}
                  className={cn(
                    "relative p-8",
                    index !== plans.length - 1 && "border-border lg:border-r",
                    plan.popular && "bg-muted/20",
                  )}
                >
                  {/* Plan Header */}
                  <div className="mb-6">
                    <h3 className="text-foreground mb-2 text-xl font-semibold">
                      {plan.name.replace(/^\w/, (c) => c.toUpperCase())}
                    </h3>
                    <div className="mb-2">
                      <span className="text-foreground text-2xl font-semibold">
                        {plan.price}
                      </span>
                      {plan.period && (
                        <span className="text-muted-foreground ml-1 text-sm">
                          {plan.period}
                        </span>
                      )}
                    </div>
                    <p className="text-muted-foreground text-sm">
                      {plan.description}
                    </p>
                    {plan.billing && (
                      <div className="mt-2 flex items-center">
                        <div className="bg-primary/20 text-primary mr-2 flex h-3 w-3 items-center justify-center rounded-full">
                          <div className="bg-primary h-1.5 w-1.5 rounded-full"></div>
                        </div>
                        <span className="text-muted-foreground text-xs">
                          {plan.billing}
                        </span>
                      </div>
                    )}
                  </div>

                  {/* Features List */}
                  <ul className="mb-8 space-y-4">
                    {plan.features.map((feature, featureIndex) => (
                      <li
                        key={featureIndex}
                        className="text-muted-foreground flex items-start text-sm"
                      >
                        <span className="text-primary mt-0.5 mr-3 flex-shrink-0">
                          <RiCheckboxCircleFill className="h-4 w-4 text-sky-500" />
                        </span>
                        {feature}
                      </li>
                    ))}
                  </ul>

                  {/* CTA Button */}
                  <Button
                    variant={plan.buttonVariant}
                    className="w-full text-sm font-semibold shadow-sm"
                  >
                    {plan.buttonText}
                  </Button>

                  {plan.name === "Business" && (
                    <p className="text-muted-foreground mt-2 text-center text-xs">
                      or{" "}
                      <span className="text-primary cursor-pointer underline">
                        contact sales
                      </span>
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Feature Comparison Table */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, amount: 0.1 }}
          transition={{ duration: 0.6, ease: "easeOut", delay: 0.6 }}
          className="mt-16"
        >
          <div className="bg-card border-border overflow-hidden rounded-lg border">
            {/* Table Header */}
            <div className="bg-muted/30 grid grid-cols-5 gap-4 p-6">
              <div></div>
              {plans.map((plan) => (
                <div
                  key={plan.name}
                  className="text-center"
                >
                  <h4 className="text-foreground text-sm font-semibold">
                    {plan.name.replace(/^\w/, (c) => c.toUpperCase())}
                  </h4>
                </div>
              ))}
            </div>

            {/* Feature Categories */}
            {comparisonFeatures.map((category) => (
              <div key={category.category}>
                {/* Category Header */}
                <div className="bg-muted/10 border-border border-t px-6 py-4">
                  <h5 className="text-foreground text-sm font-semibold">
                    {category.category}
                  </h5>
                </div>

                {/* Category Features */}
                {category.features.map((feature) => (
                  <div
                    key={feature.name}
                    className="border-border grid grid-cols-5 gap-4 border-t p-6"
                  >
                    <div className="text-muted-foreground text-sm font-normal">
                      {feature.name}
                    </div>
                    <div className="text-center">
                      {typeof feature.free === "boolean" ? (
                        feature.free ? (
                          <CheckIcon className="h-4 w-4 place-self-center text-sky-500" />
                        ) : (
                          <MinusIcon className="text-muted-foreground h-4 w-4 place-self-center" />
                        )
                      ) : (
                        <span className="text-foreground text-sm font-medium">
                          {feature.free}
                        </span>
                      )}
                    </div>
                    <div className="text-center">
                      {typeof feature.basic === "boolean" ? (
                        feature.basic ? (
                          <CheckIcon className="h-4 w-4 place-self-center text-sky-500" />
                        ) : (
                          <MinusIcon className="text-muted-foreground h-4 w-4 place-self-center" />
                        )
                      ) : (
                        <span className="text-foreground text-sm font-medium">
                          {feature.basic}
                        </span>
                      )}
                    </div>
                    <div className="text-center">
                      {typeof feature.business === "boolean" ? (
                        feature.business ? (
                          <CheckIcon className="h-4 w-4 place-self-center text-sky-500" />
                        ) : (
                          <MinusIcon className="text-muted-foreground h-4 w-4 place-self-center" />
                        )
                      ) : (
                        <span className="text-foreground text-sm font-medium">
                          {feature.business}
                        </span>
                      )}
                    </div>
                    <div className="text-center">
                      {typeof feature.enterprise === "boolean" ? (
                        feature.enterprise ? (
                          <CheckIcon className="h-4 w-4 place-self-center text-sky-500" />
                        ) : (
                          <MinusIcon className="text-muted-foreground h-4 w-4 place-self-center" />
                        )
                      ) : (
                        <span className="text-foreground text-sm font-medium">
                          {feature.enterprise}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
