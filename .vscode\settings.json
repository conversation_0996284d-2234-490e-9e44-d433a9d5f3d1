{"[astro]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[dart]": {"editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [80], "editor.selectionHighlight": false, "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": "off"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "chat.commandCenter.enabled": false, "debug.internalConsoleOptions": "openOnSessionStart", "debug.openDebug": "openOnDebugBreak", "debug.toolBarLocation": "commandCenter", "diffEditor.codeLens": true, "editor.bracketPairColorization.enabled": true, "editor.bracketPairColorization.independentColorPoolPerBracketType": true, "editor.cursorBlinking": "smooth", "editor.cursorSmoothCaretAnimation": "on", "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.folding": true, "editor.foldingImportsByDefault": false, "editor.fontFamily": "'JetBrainsMono Nerd Font', 'FiraCode Nerd Font', monospace", "editor.fontLigatures": false, "editor.fontSize": 13, "editor.fontWeight": "400", "editor.formatOnPaste": true, "editor.formatOnSave": true, "editor.formatOnSaveMode": "file", "editor.formatOnType": true, "editor.lineHeight": 1.5, "editor.minimap.showSlider": "always", "editor.renderControlCharacters": false, "editor.renderWhitespace": "none", "editor.scrollbar.horizontalScrollbarSize": 8, "editor.scrollbar.verticalScrollbarSize": 8, "editor.semanticHighlighting.enabled": true, "editor.smoothScrolling": true, "editor.tabSize": 2, "editor.wordWrap": "on", "explorer.compactFolders": false, "explorer.confirmDelete": false, "explorer.confirmDragAndDrop": false, "explorer.confirmPasteNative": false, "extensions.ignoreRecommendations": true, "files.autoGuessEncoding": true, "files.trimFinalNewlines": true, "files.trimTrailingWhitespace": true, "git.autofetch": true, "git.decorations.enabled": false, "git.enableSmartCommit": true, "git.openRepositoryInParentFolders": "never", "javascript.suggest.paths": true, "javascript.updateImportsOnFileMove.enabled": "always", "markdown.editor.pasteUrlAsFormattedLink.enabled": "smart", "markdown.validate.enabled": true, "prettier.arrowParens": "always", "prettier.bracketSameLine": false, "prettier.bracketSpacing": true, "prettier.documentSelectors": ["**/*.astro"], "prettier.htmlWhitespaceSensitivity": "strict", "prettier.jsxSingleQuote": true, "prettier.semi": true, "prettier.singleQuote": true, "prettier.tabWidth": 2, "prettier.useTabs": true, "terminal.integrated.enableMultiLinePasteWarning": "always", "terminal.integrated.fontFamily": "'GeistMono Nerd Font' ,'JetBrainsMono Nerd Font', 'FiraCode Nerd Font', monospace", "terminal.integrated.fontSize": 13.8, "totalTypeScript.hiddenTips": ["passing-generics-to-types"], "totalTypeScript.hideAllTips": true, "totalTypeScript.hideBasicTips": true, "typescript.format.enable": true, "typescript.inlayHints.parameterNames.enabled": "all", "typescript.suggest.paths": true, "typescript.updateImportsOnFileMove.enabled": "always", "workbench.activityBar.location": "top", "workbench.colorTheme": "Baby <PERSON>da", "workbench.editor.pinnedTabSizing": "shrink", "workbench.editor.tabSizing": "fit", "workbench.iconTheme": "vscode-icons", "workbench.startupEditor": "none", "workbench.statusBar.visible": false, "workbench.tree.indent": 20}